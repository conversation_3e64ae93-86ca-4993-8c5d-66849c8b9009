<cfsavecontent variable="local.dataHead">
	<cfoutput>
	<script language="javascript">
		let referralPanelsTable;

		function initReferralPanelsTable() {
			let domString = "<'row'<'col-sm-3 col-md-3'l><'col-sm-9 col-md-9'f>>" + "<'row'<'col-sm-12'tr>>" + "<'row'<'col-sm-12 col-md-5'i><'col-sm-12 col-md-7'p>>";
			referralPanelsTable = $('##referralPanelsTable').DataTable({
				"processing": true,
				"serverSide": true,
				"pageLength": 10,
				"lengthMenu": [ 10, 25, 50, 100 ],
				"language": {
					"lengthMenu": "_MENU_"
				},
				"dom": domString,
				"ajax": { 
					"url": "#local.referralPanelList#",
					"type": "post"
				},
				"autoWidth": false,
				"columns": [
					{ "data": "thePathExpanded", "width": "85%" },
					{ "data": null,
						"render": function ( data, type, row, meta ) {
							let renderData = '';
							if (type === 'display') {
								let isParentPanel = !data.thePathExpanded.includes(' \\ ');

								if(data.isSelected) {
									renderData += '<a href="javascript:removeReferralPanelFromFilter('+ data.panelID+')" id="btnRemRptPanel'+data.panelID+'" class="btn btn-xs p-1 m-1 btn-outline-danger"><i class="fa-solid fa-trash-can"></i></a>';
									renderData += '<a href="##" class="btn btn-xs p-1 m-1 btn-outline-success invisible"><i class="fa-regular fa-layer-plus"></i></a>';
								} else {
									renderData += '<a href="javascript:addToFilter('+ data.panelID+')" id="btnAddRptPanel'+data.panelID+'" class="btn btn-xs p-1 m-1 btn-outline-primary"><i class="fa-solid fa-plus"></i></a>';

									if (isParentPanel) 
										renderData += '<a href="javascript:addPanelWithSubPanels('+ data.panelID+')" title="Add This panel and sub-panels" id="btnAddRptPanelWithSubs'+data.panelID+'" class="btn btn-xs p-1 m-1 btn-outline-success"><i class="fa-regular fa-layer-plus"></i></a>';
									else 
										renderData += '<a href="##" class="btn btn-xs p-1 m-1 btn-outline-success invisible"><i class="fa-regular fa-layer-plus"></i></a>';
								}
							}
							return type === 'display' ? renderData : data;
						},
						"width": "15%",
						"className": "text-center",
						"orderable": false
					}
				],
				"order": [[0, 'asc']]
			});
		}
		function addToFilter(i) {
			var cv = $('##rplist').val().split(',');
				cv.push(i);
				cv = cv.join(',').replace(/(^\s*,)|(,\s*$)/g,'');
			$('##rplist').val(cv);
			doAddPanelToRptFilter(i, false);
		}

		function addPanelWithSubPanels(parentPanelID) {debugger;
			var cv = $('##rplist').val().split(',');
				cv.push(parentPanelID);
				cv = cv.join(',').replace(/(^\s*,)|(,\s*$)/g,'');
			$('##rplist').val(cv);
			doAddPanelToRptFilter(parentPanelID, true);
		}
		function doAddPanelToRptFilter(rpid, includeSubPanels){
			includeSubPanels = includeSubPanels || false;

			var addPanelToRptFilterResult = function(r) {
				if (r.success && r.success.toLowerCase() == 'true'){
					if (includeSubPanels) {
						// For bulk add, update all affected panels
						updatePanelIconsAfterBulkAdd(rpid, r.expandedPanelList);
					} else {
						// For single panel, just update this one
						updateSinglePanelIcon(rpid, true);
					}
					// Update parent report filter table
					top.reloadReferralPanelFilter();
				} else {
					alert('Unable to add referral panel to filter.');
					if(!includeSubPanels) {
						$('##btnAddRptPanel'+rpid).removeClass('disabled').html('<i class="fa-solid fa-plus"></i>');
					} else {
						$('##btnAddRptPanelWithSubs'+rpid).removeClass('disabled').html('<i class="fa-regular fa-layer-plus"></i>');
					}
				}
			};
			if(!includeSubPanels) {
				let addRptPanelElem = $('##btnAddRptPanel'+rpid);
				addRptPanelElem.addClass('disabled').html('Adding...');
			} else {
				let addRptPanelWithSubsElem = $('##btnAddRptPanelWithSubs'+rpid);
				addRptPanelWithSubsElem.addClass('disabled').html('Adding...');
			}

			var objParams = {
				rptID: $('##rptID').val(),
				rplist: $('##rplist').val(),
				csrID: #local.siteResourceID#,
				rptTT: '#local.reportTT#',
				includeSubPanels: includeSubPanels
			};
			TS_AJX('ADMREPORTS','saveReferralPanelFilter',objParams,addPanelToRptFilterResult,addPanelToRptFilterResult,10000,addPanelToRptFilterResult);
		}
		function reloadReferralPanelGrids(){
			referralPanelsTable.draw(false);
			top.reloadReferralPanelFilter();
		}

		function updateSinglePanelIcon(panelID, isSelected) {
			// Find the row for this panel and update its Tools column
			let table = referralPanelsTable;
			let rowData = table.data().toArray().find(row => row.panelID == panelID);

			if (rowData) {
				// Update the data object
				rowData.isSelected = isSelected;

				// Find the row index and update the cell
				let rowIndex = table.data().toArray().findIndex(row => row.panelID == panelID);
				if (rowIndex >= 0) {
					// Redraw just this row
					table.row(rowIndex).invalidate().draw(false);
				}
			}
		}

		function updatePanelIconsAfterBulkAdd(parentPanelID, expandedPanelList) {
			// Parse the expanded panel list from server response
			let panelIDs = expandedPanelList ? expandedPanelList.split(',') : [parentPanelID];

			// Update each panel's icon
			panelIDs.forEach(function(panelID) {
				if (panelID && panelID.trim()) {
					updateSinglePanelIcon(panelID.trim(), true);
				}
			});
		}
		function removeReferralPanelFromFilter(rpid){
			var removeRptPanelResult = function(r) {
				if (r.success && r.success.toLowerCase() == 'true'){
					// Update just this panel's icon instead of full reload
					updateSinglePanelIcon(rpid, false);
					// Update parent report filter table
					top.reloadReferralPanelFilter();
				} else {
					delCFElement.removeClass('disabled').html('<i class="fa-solid fa-trash-can"></i>');
					alert('Unable to remove referral panel.');
				}
			};

			let delCFElement = $('##btnRemRptPanel'+rpid);
			mca_initConfirmButton(delCFElement, function(){
				var objParams = { rptId:#local.reportID#, rptTT:'#local.reportTT#', rpid:rpid, csrID:#this.siteResourceID# };
				TS_AJX('ADMREPORTS','removeReferralPanelFilter',objParams,removeRptPanelResult,removeRptPanelResult,10000,removeRptPanelResult);
			});
		}

		function removeAllPanelsFromFlyout(){
			var removeAllPanelsResult = function(r) {
				if (r.success && r.success.toLowerCase() == 'true'){
					$('##rplist').val('');
					reloadReferralPanelGrids();
				} else {
					$('##btnRemoveAllFlyout').prop('disabled', false).html('<i class="fa-solid fa-trash-can"></i> Remove All');
					alert('Unable to remove all referral panels.');
				}
			};

			if (confirm('Are you sure you want to remove all referral panels from this report filter?')) {
				$('##btnRemoveAllFlyout').prop('disabled', true).html('<i class="fa-solid fa-spinner fa-spin"></i> Removing...');
				var objParams = { rptId:#local.reportID#, rptTT:'#local.reportTT#', csrID:#this.siteResourceID# };
				TS_AJX('ADMREPORTS','removeAllReferralPanelsFilter',objParams,removeAllPanelsResult,removeAllPanelsResult,10000,removeAllPanelsResult);
			}
		}
		$(function() {
			initReferralPanelsTable();
		});
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#local.dataHead#">

<cfoutput>
<div class="p-3">
	<table id="referralPanelsTable" class="table table-sm table-striped table-bordered" style="width:100%">
		<thead>
			<tr>
				<th>Referral Panel</th>
				<th>Tools</th>
			</tr>
		</thead>
	</table>
	<form name="frmFilter" id="frmFilter">
		<input type="hidden" name="rptID" id="rptID" value="#local.reportID#">
		<input type="hidden" name="rplist" id="rplist" value="#XMLSearch(local.otherXML,'string(/report/extra/rplist/text())')#">
	</form>
	<div class="border-top pt-3 mt-3">
		<button type="button" id="btnRemoveAllFlyout" class="btn btn-secondary" onclick="removeAllPanelsFromFlyout();">
			<i class="fa-solid fa-trash-can"></i> Remove All
		</button>
	</div>
</div>
</cfoutput>
